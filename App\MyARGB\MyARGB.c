#include "MyARGB.h"

t_Chip Chip = WS2812;

static void PWM_Write_Byte(void* buffer, uint32_t index, uint32_t value);
static void PWM_Write_HWord(void* buffer, uint32_t index, uint32_t value);
static void PWM_Write_Word(void* buffer, uint32_t index, uint32_t value);
static uint32_t PWM_Read_Byte(void* buffer, uint32_t index);
static uint32_t PWM_Read_HWord(void* buffer, uint32_t index);
static uint32_t PWM_Read_Word(void* buffer, uint32_t index);
void ARGB_TIM_DMADelayPulseCplt(DMA_HandleTypeDef *hdma);
void ARGB_TIM_DMADelayPulseHalfCplt(DMA_HandleTypeDef *hdma);
static void argb_register_instance(ARGB* this);
static ARGB* argb_get_instance(DMA_HandleTypeDef* hdma);

static ARGB* argb_instances[MAX_ARGB_INSTANCES] = {NULL};

void ARGB_New(ARGB *this, uint16_t NumPixels, uint16_t TimNum, int8_t  TimCH, TIM_HandleTypeDef *TimHandler, DMA_HandleTypeDef *DMAHandler, t_DMA_Size DMA_Size)
{
	this->NumPixels  = NumPixels;
	this->TimNum     = TimNum;
	this->TimHandler = TimHandler;
	this->TimCH		 = TimCH;
	this->DMA_Size   = DMA_Size;
	this->DMAHandler = DMAHandler;

	argb_register_instance(this);

	// Timer handler
	switch(TimCH)
	{
		case TIM_CHANNEL_1:
			this->TIM_DMA_ID = TIM_DMA_ID_CC1;
			this->TIM_DMA_CC = TIM_DMA_CC1;
			this->TIM_CCR = &(this->TimHandler->Instance->CCR1); break;
		case TIM_CHANNEL_2:
			this->TIM_DMA_ID = TIM_DMA_ID_CC2;
			this->TIM_DMA_CC = TIM_DMA_CC2;
			this->TIM_CCR = &(this->TimHandler->Instance->CCR2); break;
		case TIM_CHANNEL_3:
			this->TIM_DMA_ID = TIM_DMA_ID_CC3;
			this->TIM_DMA_CC = TIM_DMA_CC3;
			this->TIM_CCR = &(this->TimHandler->Instance->CCR3); break;
		case TIM_CHANNEL_4:
			this->TIM_DMA_ID = TIM_DMA_ID_CC4;
			this->TIM_DMA_CC = TIM_DMA_CC4;
			this->TIM_CCR = &(this->TimHandler->Instance->CCR4); break;
		default:
			for(;;);
	}

	// Timer's RCC Bus
	if(TimNum == 1 || (TimNum >= 8 && TimNum <= 11))
		this->APB = APB1;
	else
		this->APB = APB2;

	// Calculate PWM_BUF_LEN & RGB_BUF Size
	uint64_t NUM_BYTES;
	if(Chip == SK6812)
	{
		NUM_BYTES = 4 * NumPixels;
		this->PWM_BUF_LEN = 4 * 8 * 2;
	}
	else
	{
		NUM_BYTES = 3 * NumPixels;
		this->PWM_BUF_LEN = 3 * 8 * 2;
	}
	this->RGB_BUF = (volatile u8_t *)malloc(NUM_BYTES * sizeof(u8_t));
	memset((void *)this->RGB_BUF, 0, NUM_BYTES * sizeof(u8_t));

	// DMA Size,PWM_BUF
	switch(this->DMA_Size)
	{
		case BYTE:
			this->PWM_Write = PWM_Write_Byte;
			this->PWM_Read = PWM_Read_Byte;
			this->PWM_BUF = malloc(this->PWM_BUF_LEN * sizeof(uint8_t));
			//memset((void *)this->PWM_BUF, 0, this->PWM_BUF_LEN * sizeof(uint8_t));
			break;
        case HWORD:
            this->PWM_Write = PWM_Write_HWord;
            this->PWM_Read = PWM_Read_HWord;
            this->PWM_BUF = malloc(this->PWM_BUF_LEN * sizeof(uint16_t));
            //memset((void *)this->PWM_BUF, 0, this->PWM_BUF_LEN * sizeof(uint16_t));
            break;
        case WORD:
            this->PWM_Write = PWM_Write_Word;
            this->PWM_Read = PWM_Read_Word;
            this->PWM_BUF = malloc(this->PWM_BUF_LEN * sizeof(uint32_t));
            //memset((void *)this->PWM_BUF, 0, this->PWM_BUF_LEN * sizeof(uint32_t));
            break;
	}

	// Configure variables
	this->BUF_COUNTER = 0;
	this->ARGB_BR = 255;

}

void ARGB_Init(ARGB *this)
{
	/* Auto-calculation! */
	u32_t APBfq; // Clock freq
	switch(this->APB)
	{
		case APB1:
			APBfq = HAL_RCC_GetPCLK1Freq();
			APBfq *= (RCC->CFGR & RCC_CFGR_PPRE1) == 0 ? 1 : 2; break;
		case APB2:
			APBfq = HAL_RCC_GetPCLK2Freq();
			APBfq *= (RCC->CFGR & RCC_CFGR_PPRE2) == 0 ? 1 : 2; break;
	}

	switch(Chip)
	{
		case WS2811S:
			APBfq /= (uint32_t) (400 * 1000);  // 400 KHz - 2.5us
		default:
			APBfq /= (uint32_t) (800 * 1000);  // 800 KHz - 1.25us
	}

	this->TimHandler->Instance->PSC = 0;							// dummy hardcode now
	this->TimHandler->Instance->ARR = (uint16_t) (APBfq - 1);    // set timer prescaler
	this->TimHandler->Instance->EGR = 1;                         // update timer registers

	switch(Chip)
	{
		case WS2811F:
		case WS2811S:
			this->PWM_HI = (u8_t) (APBfq * 0.48) - 1;     		// Log.1 - 48% - 0.60us/1.2us
			this->PWM_LO = (u8_t) (APBfq * 0.20) - 1; break;    // Log.0 - 20% - 0.25us/0.5us
		case WS2812:
			this->PWM_HI = (u8_t) (APBfq * 0.56) - 1;           // Log.1 - 56% - 0.70us
			this->PWM_LO = (u8_t) (APBfq * 0.28) - 1; break;    // Log.0 - 28% - 0.35us
		case SK6812:
			this->PWM_HI = (u8_t) (APBfq * 0.48) - 1;           // Log.1 - 48% - 0.60us
			this->PWM_LO = (u8_t) (APBfq * 0.24) - 1; break;    // Log.0 - 24% - 0.30us
	}

	this->ARGB_LOC_ST = ARGB_READY; // Set Ready Flag
	TIM_CCxChannelCmd(this->TimHandler->Instance, this->TimCH, TIM_CCx_ENABLE); // Enable GPIO to IDLE state
	HAL_Delay(1); // Make some delay

}

void ARGB_Clear(ARGB *this) {
	ARGB_FillRGB(this, 0, 0, 0);
//	if (Chip == SK6812) {
//		ARGB_FillWhite(0);
//	}
}

void ARGB_FillRGB(ARGB *this, u8_t r, u8_t g, u8_t b) {
    for (volatile u16_t i = 0; i < this->NumPixels; i++)
        ARGB_SetRGB(this, i, r, g, b);
}

void ARGB_SetRGB(ARGB *this, u16_t i, u8_t r, u8_t g, u8_t b)
{
	// overflow protection
	if (i >= this->NumPixels)
	{
		u16_t _i = i / this->NumPixels;
		i -= _i * this->NumPixels;
	}

	// set brightness
	r /= 256 / ((u16_t) this->ARGB_BR + 1);
	g /= 256 / ((u16_t) this->ARGB_BR + 1);
	b /= 256 / ((u16_t) this->ARGB_BR + 1);


    // Subpixel chain order
	u8_t subp1, subp2, subp3;
	switch(Chip)
	{
		case SK6812:
		case WS2811F:
		case WS2811S:
			subp1 = r; subp2 = g; subp3 = b; break;
		default:
			subp1 = g; subp2 = r; subp3 = b; break;
	}

	// RGB or RGBW
	switch(Chip) // todo Нужно ли привидение типов здесь?
	{
		case SK6812:
			this->RGB_BUF[4 * i] = subp1; 	  		 // subpixel 1
			this->RGB_BUF[4 * i + 1] = subp2; 		 // subpixel 2
			this->RGB_BUF[4 * i + 2] = subp3; break; // subpixel 3
		default:
			this->RGB_BUF[3 * i] = subp1;     		 // subpixel 1
			this->RGB_BUF[3 * i + 1] = subp2; 		 // subpixel 2
			this->RGB_BUF[3 * i + 2] = subp3; break; // subpixel 3
	}

}

ARGB_STATE ARGB_Show(ARGB *this)
{
	this->ARGB_LOC_ST = ARGB_BUSY;
	if (this->BUF_COUNTER != 0 || this->DMAHandler->State != HAL_DMA_STATE_READY)
	{
		return ARGB_BUSY;
	}
	else
	{
		for (volatile u8_t i = 0; i < 8; i++)
		{
			// set first transfer from first values
			this->PWM_Write(this->PWM_BUF, i, (((this->RGB_BUF[0] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
			this->PWM_Write(this->PWM_BUF, i + 8, (((this->RGB_BUF[1] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
			this->PWM_Write(this->PWM_BUF, i + 16, (((this->RGB_BUF[2] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
			this->PWM_Write(this->PWM_BUF, i + 24, (((this->RGB_BUF[3] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
			this->PWM_Write(this->PWM_BUF, i + 32, (((this->RGB_BUF[4] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
			this->PWM_Write(this->PWM_BUF, i + 40, (((this->RGB_BUF[5] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);

			if (Chip == SK6812) {
				this->PWM_Write(this->PWM_BUF, i + 48, (((this->RGB_BUF[6] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
			    this->PWM_Write(this->PWM_BUF, i + 56, (((this->RGB_BUF[7] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
			}
		}

		HAL_StatusTypeDef DMA_Send_Stat = HAL_ERROR;
        while (DMA_Send_Stat != HAL_OK)
        {
            if (TIM_CHANNEL_STATE_GET(this->TimHandler, this->TimCH) == HAL_TIM_CHANNEL_STATE_BUSY) {
                DMA_Send_Stat = HAL_BUSY;
                continue;
            } else if (TIM_CHANNEL_STATE_GET(this->TimHandler, this->TimCH) == HAL_TIM_CHANNEL_STATE_READY) {
                TIM_CHANNEL_STATE_SET(this->TimHandler, this->TimCH, HAL_TIM_CHANNEL_STATE_BUSY);
            } else {
                DMA_Send_Stat = HAL_ERROR;
                continue;
            }

			this->TimHandler->hdma[this->TIM_DMA_ID]->XferCpltCallback = ARGB_TIM_DMADelayPulseCplt;
			this->TimHandler->hdma[this->TIM_DMA_ID]->XferHalfCpltCallback = ARGB_TIM_DMADelayPulseHalfCplt;
			this->TimHandler->hdma[this->TIM_DMA_ID]->XferErrorCallback = TIM_DMAError;

			if (HAL_DMA_Start_IT(this->TimHandler->hdma[this->TIM_DMA_ID], (uint32_t)this->PWM_BUF,
											 (uint32_t)this->TIM_CCR,
											 (uint16_t)this->PWM_BUF_LEN) != HAL_OK) {
				DMA_Send_Stat = HAL_ERROR;
				continue;
			}

			__HAL_TIM_ENABLE_DMA(this->TimHandler, this->TIM_DMA_CC);
			if (IS_TIM_BREAK_INSTANCE(this->TimHandler->Instance) != RESET)
			{
				__HAL_TIM_MOE_ENABLE(this->TimHandler);
			}

			if (IS_TIM_SLAVE_INSTANCE(this->TimHandler->Instance))
			{
				uint32_t tmpsmcr = this->TimHandler->Instance->SMCR & TIM_SMCR_SMS;
				if (!IS_TIM_SLAVEMODE_TRIGGER_ENABLED(tmpsmcr)) {
					__HAL_TIM_ENABLE(this->TimHandler);
				}
			}
			else
			{
				__HAL_TIM_ENABLE(this->TimHandler);
			}

			DMA_Send_Stat = HAL_OK;
        }
    }

    this->BUF_COUNTER = 2;
    return ARGB_OK;
}

void ARGB_TIM_DMADelayPulseCplt(DMA_HandleTypeDef *hdma)
{
	ARGB *this = argb_get_instance(hdma);
	TIM_HandleTypeDef *htim = (TIM_HandleTypeDef *) ((DMA_HandleTypeDef *) hdma)->Parent;

    // if wrong handlers
    if (hdma != this->DMAHandler) return;
    if (htim != this->TimHandler) return;

    // if no data to transmit - return
    if (this->BUF_COUNTER == 0) return;

    // Обработка канала
    if (hdma == htim->hdma[TIM_DMA_ID_CC1]) {
        htim->Channel = HAL_TIM_ACTIVE_CHANNEL_1;
        if (hdma->Init.Mode == DMA_NORMAL) {
            TIM_CHANNEL_STATE_SET(htim, TIM_CHANNEL_1, HAL_TIM_CHANNEL_STATE_READY);
        }
    } else if (hdma == htim->hdma[TIM_DMA_ID_CC2]) {
        htim->Channel = HAL_TIM_ACTIVE_CHANNEL_2;
        if (hdma->Init.Mode == DMA_NORMAL) {
            TIM_CHANNEL_STATE_SET(htim, TIM_CHANNEL_2, HAL_TIM_CHANNEL_STATE_READY);
        }
    } else if (hdma == htim->hdma[TIM_DMA_ID_CC3]) {
        htim->Channel = HAL_TIM_ACTIVE_CHANNEL_3;
        if (hdma->Init.Mode == DMA_NORMAL) {
            TIM_CHANNEL_STATE_SET(htim, TIM_CHANNEL_3, HAL_TIM_CHANNEL_STATE_READY);
        }
    } else if (hdma == htim->hdma[TIM_DMA_ID_CC4]) {
        htim->Channel = HAL_TIM_ACTIVE_CHANNEL_4;
        if (hdma->Init.Mode == DMA_NORMAL) {
            TIM_CHANNEL_STATE_SET(htim, TIM_CHANNEL_4, HAL_TIM_CHANNEL_STATE_READY);
        }
    }

    // if data transfer
    if (this->BUF_COUNTER < this->NumPixels)
    {
    	// fill second part of buffer
    	for (volatile uint8_t i = 0; i < 8; i++)
    	{
    		if(Chip == SK6812)
    		{
                this->PWM_Write(this->PWM_BUF, i + 32, (((this->RGB_BUF[4 * this->BUF_COUNTER] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 40, (((this->RGB_BUF[4 * this->BUF_COUNTER + 1] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 48, (((this->RGB_BUF[4 * this->BUF_COUNTER + 2] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 56, (((this->RGB_BUF[4 * this->BUF_COUNTER + 3] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
    		}
    		else
    		{
                this->PWM_Write(this->PWM_BUF, i + 24, (((this->RGB_BUF[3 * this->BUF_COUNTER] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 32, (((this->RGB_BUF[3 * this->BUF_COUNTER + 1] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 40, (((this->RGB_BUF[3 * this->BUF_COUNTER + 2] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
    		}
    	}
    	this->BUF_COUNTER++;
    }

    // if RET transfer
    else if (this->BUF_COUNTER < this->NumPixels + 2)
    {
        //memset((void *)&this->PWM_BUF[this->PWM_BUF_LEN / 2], 0, (this->PWM_BUF_LEN / 2) * sizeof(uint8_t)); // second part

    	switch (this->DMA_Size)
    	{
    	    case BYTE:
    	    	memset((uint8_t *)&((uint8_t *)this->PWM_BUF)[this->PWM_BUF_LEN / 2], 0, (this->PWM_BUF_LEN / 2) * sizeof(uint8_t));
    	        break;
    	    case HWORD:
    	    	memset((uint16_t *)&((uint16_t *)this->PWM_BUF)[this->PWM_BUF_LEN / 2], 0, (this->PWM_BUF_LEN / 2) * sizeof(uint16_t));
    	        break;
    	    case WORD:
    	    	memset((uint32_t *)&((uint32_t *)this->PWM_BUF)[this->PWM_BUF_LEN / 2], 0, (this->PWM_BUF_LEN / 2) * sizeof(uint32_t));
    	        break;
    	    default:
    	        break;
    	}


        this->BUF_COUNTER++;
    }

    // if END of transfer
    else
    {
    	this->BUF_COUNTER = 0;

    	// STOP DMA:
    	__HAL_TIM_DISABLE_DMA(htim, this->TIM_DMA_CC);
    	(void)HAL_DMA_Abort_IT(htim->hdma[this->TIM_DMA_ID]);

    	if (IS_TIM_BREAK_INSTANCE(htim->Instance) != RESET) {
    		// Disable the Main Output
    		__HAL_TIM_MOE_DISABLE(htim);
    	}
    	// Disable the Peripheral
    	__HAL_TIM_DISABLE(htim);
    	// Set the TIM channel state
    	TIM_CHANNEL_STATE_SET(htim, this->TimCH, HAL_TIM_CHANNEL_STATE_READY);
    	this->ARGB_LOC_ST = ARGB_READY;
    }
    htim->Channel = HAL_TIM_ACTIVE_CHANNEL_CLEARED;
}

void ARGB_TIM_DMADelayPulseHalfCplt(DMA_HandleTypeDef *hdma)
{
	ARGB *this = argb_get_instance(hdma);
	TIM_HandleTypeDef *htim = (TIM_HandleTypeDef *) ((DMA_HandleTypeDef *) hdma)->Parent;

    // if wrong handlers
    if (hdma != this->DMAHandler || htim != this->TimHandler) return;

    // if no data to transmit - return
    if (this->BUF_COUNTER == 0) return;

    // if data transfer
    if (this->BUF_COUNTER < this->NumPixels)
    {
    	for (volatile uint8_t i = 0; i < 8; i++)
    	{
    		if(Chip == SK6812)
    		{
                this->PWM_Write(this->PWM_BUF, i, (((this->RGB_BUF[4 * this->BUF_COUNTER] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 8, (((this->RGB_BUF[4 * this->BUF_COUNTER + 1] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 16, (((this->RGB_BUF[4 * this->BUF_COUNTER + 2] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 24, (((this->RGB_BUF[4 * this->BUF_COUNTER + 3] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
    		}
    		else
    		{
                this->PWM_Write(this->PWM_BUF, i, (((this->RGB_BUF[3 * this->BUF_COUNTER] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 8, (((this->RGB_BUF[3 * this->BUF_COUNTER + 1] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
                this->PWM_Write(this->PWM_BUF, i + 16, (((this->RGB_BUF[3 * this->BUF_COUNTER + 2] << i) & 0x80) > 0) ? this->PWM_HI : this->PWM_LO);
    		}
    	}
    	this->BUF_COUNTER++;
    }

    // if RET transfer
    else if (this->BUF_COUNTER < this->NumPixels + 2)
    {
    	// second part
        //memset((uint8_t *) &this->PWM_BUF[0], 0, (this->PWM_BUF_LEN / 2) * sizeof(uint8_t));

    	switch (this->DMA_Size)
    	{
    	    case BYTE:
    	    	memset((uint8_t *)this->PWM_BUF, 0, (this->PWM_BUF_LEN / 2) * sizeof(uint8_t));
    	        break;
    	    case HWORD:
    	    	memset((uint16_t *)this->PWM_BUF, 0, (this->PWM_BUF_LEN / 2) * sizeof(uint16_t));
    	        break;
    	    case WORD:
    	    	memset((uint32_t *)this->PWM_BUF, 0, (this->PWM_BUF_LEN / 2) * sizeof(uint32_t));
    	        break;
    	    default:
    	        break;
    	}

        this->BUF_COUNTER++;
    }
}

static void argb_register_instance(ARGB* this)
{
    for (int i = 0; i < MAX_ARGB_INSTANCES; i++)
    {
        if (argb_instances[i] == NULL)
        {
            argb_instances[i] = this;
            break;
        }
    }
}

static ARGB* argb_get_instance(DMA_HandleTypeDef* hdma)
{
    for (int i = 0; i < MAX_ARGB_INSTANCES; i++)
    {
        if (argb_instances[i] != NULL && argb_instances[i]->DMAHandler == hdma)
        {
            return argb_instances[i];
        }
    }
    return NULL;
}


static void PWM_Write_Byte(void* buffer, uint32_t index, uint32_t value) {
    ((uint8_t*)buffer)[index] = (uint8_t)value;
}

static void PWM_Write_HWord(void* buffer, uint32_t index, uint32_t value) {
    ((uint16_t*)buffer)[index] = (uint16_t)value;
}

static void PWM_Write_Word(void* buffer, uint32_t index, uint32_t value) {
    ((uint32_t*)buffer)[index] = value;
}

static uint32_t PWM_Read_Byte(void* buffer, uint32_t index) {
    return ((uint8_t*)buffer)[index];
}

static uint32_t PWM_Read_HWord(void* buffer, uint32_t index) {
    return ((uint16_t*)buffer)[index];
}

static uint32_t PWM_Read_Word(void* buffer, uint32_t index) {
    return ((uint32_t*)buffer)[index];
}

